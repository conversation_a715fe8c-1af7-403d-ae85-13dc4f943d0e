// eBay-specific product data extractor

window.EbayExtractor = {
  // eBay-specific selectors
  selectors: {
    title: [
      '#x-title-label-lbl',
      '.x-item-title-label',
      'h1[data-testid="x-item-title-label"]',
      '.notranslate'
    ],
    images: [
      '#icImg',
      '.img img',
      '#mainImgHldr img',
      '.ux-image-carousel img',
      '.ux-image-filmstrip img'
    ],
    seller: [
      '.mbg-nw',
      '.seller-persona .mbg-nw',
      '[data-testid="seller-name"]',
      '.seller-info .mbg-nw'
    ],
    description: [
      '#desc_div',
      '.item-description',
      '[data-testid="item-description"]'
    ]
  },

  // Extract product data from eBay listing page
  extractProductData() {
    try {
      console.log('Extracting eBay product data...');

      const productData = {
        title: this.extractTitle(),
        productUrl: CommonExtractor.getCurrentUrl(),
        marketplace: 'ebay',
        sellerName: this.extractSeller(),
        images: this.extractImages(),
        metadata: this.extractMetadata()
      };

      // Validate the extracted data
      const validation = CommonExtractor.validateProductData(productData);
      
      if (!validation.isValid) {
        console.error('eBay product data validation failed:', validation.errors);
        CommonExtractor.showNotification(
          'Failed to extract complete product data: ' + validation.errors.join(', '),
          'error'
        );
        return null;
      }

      console.log('eBay product data extracted successfully:', productData);
      return productData;

    } catch (error) {
      console.error('Error extracting eBay product data:', error);
      CommonExtractor.showNotification('Error extracting product data: ' + error.message, 'error');
      return null;
    }
  },

  extractTitle() {
    for (const selector of this.selectors.title) {
      const title = CommonExtractor.extractText(selector);
      if (title && title.length > 5) {
        return CommonExtractor.cleanText(title);
      }
    }
    
    // Fallback to page title
    const pageTitle = document.title;
    if (pageTitle && !pageTitle.includes('eBay')) {
      return CommonExtractor.cleanText(pageTitle.split('|')[0]);
    }
    
    throw new Error('Could not extract product title');
  },

  extractSeller() {
    for (const selector of this.selectors.seller) {
      const seller = CommonExtractor.extractText(selector);
      if (seller && seller.length > 1) {
        return CommonExtractor.cleanText(seller);
      }
    }
    
    return null;
  },

  extractImages() {
    const images = CommonExtractor.extractImages(this.selectors.images);
    
    // eBay-specific image processing
    return images.map((image, index) => {
      let imageUrl = image.imageUrl;
      
      // Convert eBay thumbnail URLs to full size
      if (imageUrl.includes('s-l64')) {
        imageUrl = imageUrl.replace('s-l64', 's-l1600');
      } else if (imageUrl.includes('s-l225')) {
        imageUrl = imageUrl.replace('s-l225', 's-l1600');
      } else if (imageUrl.includes('s-l300')) {
        imageUrl = imageUrl.replace('s-l300', 's-l1600');
      } else if (imageUrl.includes('s-l500')) {
        imageUrl = imageUrl.replace('s-l500', 's-l1600');
      }
      
      return {
        ...image,
        imageUrl: imageUrl,
        isPrimary: index === 0
      };
    });
  },

  extractMetadata() {
    const metadata = {};
    
    // Extract description
    const description = this.extractDescription();
    if (description) {
      metadata.description = description;
    }
    
    return metadata;
  },

  extractDescription() {
    for (const selector of this.selectors.description) {
      const description = CommonExtractor.extractText(selector);
      if (description && description.length > 20) {
        return CommonExtractor.cleanText(description).substring(0, 500);
      }
    }
    
    return null;
  },

  // Check if current page is an eBay product page
  isProductPage() {
    return (window.location.pathname.includes('/itm/') || 
            window.location.pathname.includes('/p/')) && 
           window.location.hostname.includes('ebay.com');
  }
};

// Auto-extract when page loads (for manual crawling)
if (EbayExtractor.isProductPage()) {
  // Wait for page to fully load
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => {
        console.log('eBay product page detected and ready for extraction');
      }, 1000);
    });
  } else {
    console.log('eBay product page detected and ready for extraction');
  }
}
